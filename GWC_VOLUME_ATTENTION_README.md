# GWC Volume Attention Implementation

## 概述

本实现在 `core/vggt_stereo.py` 的 `hierarchical_disparity_estimation` 函数中添加了双重自注意力机制，用于增强 GWC (Group-wise Correlation) volume 的特征表示。

## 实现细节

### 1. 双重自注意力架构

实现了两个层次的自注意力：

1. **视差维度自注意力 (Disparity Dimension Attention)**
   - 输入形状：`[B*H*W, 8, 192]`
   - 在 192 个视差通道上进行自注意力
   - 使用 8 个注意力头，MLP 比例为 4.0

2. **空间窗口自注意力 (Spatial Windowed Attention)**
   - 输入形状：`[B*192, 8, H, W]`
   - 使用 7×7 窗口进行空间自注意力
   - 在 8 个视差级别上进行自注意力
   - 使用 4 个注意力头，MLP 比例为 4.0

### 2. 数据流转换

```
原始 GWC Volume: [B, 8, 192, H, W]
    ↓
视差维度注意力: [B*H*W, 192, 8] → [B*H*W, 8, 192] → 自注意力 → [B*H*W, 192, 8]
    ↓
空间窗口注意力: [B*192, 8, H, W] → 7×7窗口自注意力 → [B*192, 8, H, W]
    ↓
最终输出: [B, 8, 192, H, W]
```

### 3. 关键代码修改

#### 在 `core/vggt_stereo.py` 中的修改：

1. **导入必要模块**：
```python
from core.dssm import DSSM_update, apply_windowed_attention
from vggt.layers.block import Block
```

2. **在 `__init__` 方法中添加注意力模块**：
```python
# GWC Volume Attention modules
self.disparity_attention = Block(
    dim=192, num_heads=8, mlp_ratio=4.0, qkv_bias=True,
    drop=0.0, attn_drop=0.0, drop_path=0.0
)

self.spatial_attention = Block(
    dim=8, num_heads=4, mlp_ratio=4.0, qkv_bias=True,
    drop=0.0, attn_drop=0.0, drop_path=0.0
)
```

3. **在 `hierarchical_disparity_estimation` 方法中应用注意力**：
```python
def hierarchical_disparity_estimation(self, match_left, match_right, features_left):
    # 构建 GWC volume
    gwc_volume = build_gwc_volume(match_left, match_right, 768//4, 8)  
    
    # 应用双重自注意力
    b, num_groups, maxdisp, h, w = gwc_volume.shape
    
    # 步骤1：视差维度自注意力
    gwc_reshaped = gwc_volume.permute(0, 3, 4, 2, 1).contiguous()
    gwc_reshaped = gwc_reshaped.view(b * h * w, maxdisp, num_groups)
    gwc_disp = gwc_reshaped.permute(0, 2, 1)
    gwc_disp_attn = self.disparity_attention(gwc_disp)
    gwc_disp_attn = gwc_disp_attn.permute(0, 2, 1)
    
    # 步骤2：空间窗口自注意力
    gwc_spatial = gwc_disp_attn.view(b, h, w, maxdisp, num_groups)
    gwc_spatial = gwc_spatial.permute(0, 3, 4, 1, 2)
    gwc_spatial = gwc_spatial.contiguous().view(b * maxdisp, num_groups, h, w)
    gwc_spatial_attn = apply_windowed_attention(gwc_spatial, self.spatial_attention, window_size=7)
    
    # 重塑回原始格式
    gwc_spatial_attn = gwc_spatial_attn.view(b, maxdisp, num_groups, h, w)
    gwc_volume_attn = gwc_spatial_attn.permute(0, 2, 1, 3, 4)
    
    # 使用增强后的 GWC volume 进行分类
    volume = self.classifier(gwc_volume_attn).squeeze(1)
    # ... 其余代码保持不变
```

## 测试结果

### 测试配置
- 输入尺寸：`[1, 96, 112, 112]` (match_left, match_right)
- GWC Volume 尺寸：`[1, 8, 192, 112, 112]`
- 设备：CUDA

### 性能指标
- **总参数量**：456,536
- **注意力参数**：445,736 (97.6%)
- **分类器参数**：10,800 (2.4%)

### 输出验证
- ✅ 形状保持：输入和输出的 GWC volume 形状一致
- ✅ 视差范围合理：76.68 - 101.70
- ✅ 概率分布正常：softmax 输出正确

## 优势

1. **增强特征表示**：通过双重自注意力机制，模型能够更好地捕获视差维度和空间维度的长距离依赖关系

2. **保持计算效率**：
   - 使用窗口注意力减少空间维度的计算复杂度
   - 分离的注意力机制避免了全局注意力的二次复杂度

3. **模块化设计**：注意力模块可以独立训练和调优

4. **兼容性**：完全兼容现有的 VGGT 立体匹配架构

## 使用方法

1. 确保已安装必要的依赖：
   - PyTorch
   - VGGT 模块

2. 运行测试：
```bash
python test_gwc_attention.py          # 基础注意力测试
python test_hierarchical_disparity.py # 完整流程测试
```

3. 在实际模型中使用：
```python
from core.vggt_stereo import VGGT_Ste

model = VGGT_Ste(args)
fine_disp, prob = model.hierarchical_disparity_estimation(match_left, match_right, features_left)
```

## 注意事项

1. **内存使用**：注意力机制会增加内存使用，特别是在大分辨率图像上
2. **训练稳定性**：建议使用较小的学习率来训练注意力模块
3. **窗口大小**：7×7 窗口大小是经验值，可以根据具体任务调整

## 文件结构

```
core/
├── vggt_stereo.py          # 主要实现文件
├── dssm.py                 # 窗口注意力函数
└── submodule.py            # GWC volume 构建函数

test_gwc_attention.py       # 基础测试脚本
test_hierarchical_disparity.py  # 完整测试脚本
```
