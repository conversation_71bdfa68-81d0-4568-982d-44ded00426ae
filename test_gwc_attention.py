#!/usr/bin/env python3
"""
Test script for GWC Volume Attention implementation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
sys.path.append('./vggt')

from core.submodule import build_gwc_volume
from core.dssm import apply_windowed_attention
from vggt.layers.block import Block


def test_gwc_volume_attention():
    """Test the GWC volume attention implementation"""
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create test data
    batch_size = 2
    height, width = 64, 64  # Smaller for testing
    channels = 96  # Match feature channels
    
    # Create dummy match features
    match_left = torch.randn(batch_size, channels, height, width).to(device)
    match_right = torch.randn(batch_size, channels, height, width).to(device)
    
    print(f"Input shapes:")
    print(f"  match_left: {match_left.shape}")
    print(f"  match_right: {match_right.shape}")
    
    # Build GWC volume
    maxdisp = 192  # 768//4
    num_groups = 8
    gwc_volume = build_gwc_volume(match_left, match_right, maxdisp, num_groups)
    print(f"  gwc_volume: {gwc_volume.shape}")  # Should be [B, 8, 192, H, W]
    
    # Create attention modules
    disparity_attention = Block(
        dim=192,  # 192 channels
        num_heads=8,
        mlp_ratio=4.0,
        qkv_bias=True,
        drop=0.0,
        attn_drop=0.0,
        drop_path=0.0
    ).to(device)
    
    spatial_attention = Block(
        dim=8,  # 8 disparity levels
        num_heads=4,
        mlp_ratio=4.0,
        qkv_bias=True,
        drop=0.0,
        attn_drop=0.0,
        drop_path=0.0
    ).to(device)
    
    print(f"\nApplying dual self-attention...")
    
    # Apply dual self-attention to GWC volume
    b, num_groups_actual, maxdisp_actual, h, w = gwc_volume.shape  # [B, 8, 192, H, W]
    
    print(f"  Original shape: {gwc_volume.shape}")
    
    # Step 1: Reshape for disparity dimension attention
    # [B, 8, 192, H, W] -> [B*H*W, 192, 8]
    gwc_reshaped = gwc_volume.permute(0, 3, 4, 2, 1).contiguous()  # [B, H, W, 192, 8]
    gwc_reshaped = gwc_reshaped.view(b * h * w, maxdisp_actual, num_groups_actual)  # [B*H*W, 192, 8]
    
    print(f"  Reshaped for disparity attention: {gwc_reshaped.shape}")
    
    # Apply disparity dimension self-attention
    # Reshape for attention: [B*H*W, 192, 8] -> [B*H*W, 8, 192]
    gwc_disp = gwc_reshaped.permute(0, 2, 1)  # [B*H*W, 8, 192]
    print(f"  Input to disparity attention: {gwc_disp.shape}")
    
    with torch.no_grad():
        gwc_disp_attn = disparity_attention(gwc_disp)  # [B*H*W, 8, 192]
    print(f"  Output from disparity attention: {gwc_disp_attn.shape}")
    
    # Reshape back: [B*H*W, 8, 192] -> [B*H*W, 192, 8]
    gwc_disp_attn = gwc_disp_attn.permute(0, 2, 1)  # [B*H*W, 192, 8]
    
    # Step 2: Reshape for spatial windowed attention
    # [B*H*W, 192, 8] -> [B*192, H, W, 8] -> [B*192, 8, H, W]
    gwc_spatial = gwc_disp_attn.view(b, h, w, maxdisp_actual, num_groups_actual)  # [B, H, W, 192, 8]
    gwc_spatial = gwc_spatial.permute(0, 3, 4, 1, 2)  # [B, 192, 8, H, W]
    gwc_spatial = gwc_spatial.contiguous().view(b * maxdisp_actual, num_groups_actual, h, w)  # [B*192, 8, H, W]
    
    print(f"  Reshaped for spatial attention: {gwc_spatial.shape}")
    
    # Apply spatial windowed self-attention with window size 7
    with torch.no_grad():
        gwc_spatial_attn = apply_windowed_attention(gwc_spatial, spatial_attention, window_size=7)
    print(f"  Output from spatial attention: {gwc_spatial_attn.shape}")
    
    # Reshape back to original format: [B*192, 8, H, W] -> [B, 8, 192, H, W]
    gwc_spatial_attn = gwc_spatial_attn.view(b, maxdisp_actual, num_groups_actual, h, w)  # [B, 192, 8, H, W]
    gwc_volume_attn = gwc_spatial_attn.permute(0, 2, 1, 3, 4)  # [B, 8, 192, H, W]
    
    print(f"  Final output shape: {gwc_volume_attn.shape}")
    print(f"  Shape preserved: {gwc_volume_attn.shape == gwc_volume.shape}")
    
    # Test with classifier
    classifier = nn.Sequential(
        nn.Conv3d(8, 16, 3, 1, 1, bias=False),
        nn.GELU(),
        nn.InstanceNorm3d(16),
        nn.Conv3d(16, 16, 3, 1, 1, bias=False),
        nn.GELU(),
        nn.InstanceNorm3d(16),
        nn.Conv3d(16, 1, 3, 1, 1, bias=False)
    ).to(device)
    
    with torch.no_grad():
        volume = classifier(gwc_volume_attn).squeeze(1)
        prob = F.softmax(volume, dim=1)
    
    print(f"  Classifier output shape: {volume.shape}")
    print(f"  Probability shape: {prob.shape}")
    
    # Calculate parameter counts
    disp_params = sum(p.numel() for p in disparity_attention.parameters())
    spatial_params = sum(p.numel() for p in spatial_attention.parameters())
    total_params = disp_params + spatial_params
    
    print(f"\nParameter counts:")
    print(f"  Disparity attention: {disp_params:,}")
    print(f"  Spatial attention: {spatial_params:,}")
    print(f"  Total attention params: {total_params:,}")
    
    print(f"\nGWC Volume Attention test completed successfully!")
    
    return gwc_volume_attn


if __name__ == "__main__":
    test_gwc_volume_attention()
