import torch
import torch.nn as nn
import torch.nn.functional as F
import sys

# Core components
from core.update import ConvGRU, BasicMultiUpdateBlock, UpdateBlock_Detail, UpdateBlock_Occ
from core.geometry import OptimizedCorrelationOnlyGeoEncoding

from core.submodule import *
from core.warp import disp_warp
from core.config import ModelConfig
from core.utils.utils import pool2x, interp
from core.attn import FeatureEnhancer, MultiScaleFeatureEnhancer
from core.upsample import DisparityUpsampler
from core.dssm import DSSM_update, apply_windowed_attention
from vggt.layers.block import Block
import math

# Monocular depth components
sys.path.append('./Depth-Anything-V2-list3')
sys.path.append('./vggt')
from depth_anything_v2.dpt import DepthAnythingV2, DepthAnythingV2_decoder
from vggt import VGGT




class VGGT_Ste(nn.Module):
    def __init__(self, args):
        super().__init__()
        self.args = args
        
        # Initialize model configurations
        self.intermediate_layer_idx = ModelConfig.INTERMEDIATE_LAYER_IDX
        self.mono_model_configs = ModelConfig.MONO_MODEL_CONFIGS
        
        # Get dimension configuration
        dim_list_ = self.mono_model_configs[self.args.encoder]['features']
        self.dim_list = [dim_list_]
    
        # Initialize context networks and feature transfer modules
        self.context_zqr_convs = nn.ModuleList([
            nn.Conv2d(self.args.hidden_dims[i], self.args.hidden_dims[i]*3, 3, padding=3//2) 
            for i in range(self.args.n_gru_layers)
        ])
        
        # self.feat_transfer_cnet1 = nn.Sequential(
        #     BasicConv_IN(256, 128, kernel_size=3, stride=1, padding=1),
        #     nn.Conv2d(128, 96, 3, 1, 1, bias=False),
        #     nn.InstanceNorm2d(96))
        
        # self.feat_transfer_cnet2 = nn.Sequential(
        #     BasicConv_IN(256, 128, kernel_size=3, stride=1, padding=1),
        #     nn.Conv2d(128, 96, 3, 1, 1, bias=False),
        #     nn.InstanceNorm2d(96))
        
        # self.feat_transfer_cnet3 = nn.Sequential(
        #     BasicConv_IN(256, 128, kernel_size=3, stride=1, padding=1),
        #     nn.Conv2d(128, 96, 3, 1, 1, bias=False),
        #     nn.InstanceNorm2d(96))
        
        self.feat_transfer_context = nn.Sequential(
            BasicConv_IN(256, 32, kernel_size=3, stride=1, padding=1),
            nn.Conv2d(32, 32, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(32))

        # Initialize CNN feature extraction layers
        self.stem_2 = nn.Sequential(
            BasicConv_IN(3, 32, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(32, 32, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(32), nn.ReLU())

        # self.stem_4_1 = nn.Sequential(
        #     BasicConv_IN(32, 32, kernel_size=3, stride=2, padding=1),
        #     nn.Conv2d(32, 32, 3, 1, 1, bias=False),
        #     nn.InstanceNorm2d(32), nn.ReLU())

        # self.stem_4_2 = nn.Sequential(
        #     BasicConv_IN(32, 32, kernel_size=3, stride=2, padding=1),
        #     nn.Conv2d(32, 32, 3, 1, 1, bias=False),
        #     nn.InstanceNorm2d(32), nn.ReLU())
        
        # self.stem_4_3 = nn.Sequential(
        #     BasicConv_IN(32, 32, kernel_size=3, stride=2, padding=1),
        #     nn.Conv2d(32, 32, 3, 1, 1, bias=False),
        #     nn.InstanceNorm2d(32), nn.ReLU())
    
        # Upsampling components
        self.spx = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1))
        self.spx_2 = Conv2x_IN(24, 32, True)
        self.spx_4 = nn.Sequential(
            BasicConv_IN(256, 24, kernel_size=3, stride=1, padding=1),
            nn.Conv2d(24, 24, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(24), nn.ReLU()
        )
        self.spx_2_gru = Conv2x(32, 32, True)
        self.spx_gru = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1))
        # self.context_conv = BasicConv_IN(256, 32, kernel_size=3, padding=1, stride=1)

        # Stereo matching components
        self.conv = BasicConv_IN(256, 96, kernel_size=3, padding=1, stride=1)
        self.desc = nn.Conv2d(96, 96, kernel_size=1, padding=0, stride=1)

        self.classifier = nn.Sequential(nn.Conv3d(8, 16, 3, 1, 1, bias=False),
                                        nn.GELU(),
                                        nn.InstanceNorm3d(16),
                                        nn.Conv3d(16, 16, 3, 1, 1, bias=False),
                                        nn.GELU(),
                                        nn.InstanceNorm3d(16),
                                        nn.Conv3d(16, 1, 3, 1, 1, bias=False))

        self.extractor = VGGT()
        self.load_vggt_pretrained_weights()
        self.update = DSSM_update()

        # GWC Volume Attention modules
        # Disparity dimension self-attention (for 192 channels)
        self.disparity_attention = Block(
            dim=192,  # 192 channels
            num_heads=4,
            mlp_ratio=4.0,
            qkv_bias=True,
            drop=0.0,
            attn_drop=0.0,
            drop_path=0.0
        )

    def load_vggt_pretrained_weights(self):
        """
        Load pretrained weights for VGGT model.
        Only loads weights that exist and match in both the checkpoint and the model.
        """
        import os

        checkpoint_path = "vggt/checkpoint/model.pt"

        if not os.path.exists(checkpoint_path):
            print(f"Warning: VGGT checkpoint file not found at {checkpoint_path}")
            return

        try:
            # Load checkpoint
            checkpoint = torch.load(checkpoint_path, map_location='cpu')

            # Extract state dict from checkpoint
            if isinstance(checkpoint, dict):
                if 'model' in checkpoint:
                    pretrained_state_dict = checkpoint['model']
                elif 'state_dict' in checkpoint:
                    pretrained_state_dict = checkpoint['state_dict']
                else:
                    pretrained_state_dict = checkpoint
            else:
                pretrained_state_dict = checkpoint

            # Get current model state dict
            model_state_dict = self.extractor.state_dict()

            # Filter and load matching weights
            matched_weights = {}
            unmatched_keys = []

            for key, value in pretrained_state_dict.items():
                if key in model_state_dict:
                    if model_state_dict[key].shape == value.shape:
                        matched_weights[key] = value
                    else:
                        unmatched_keys.append(f"{key}: shape mismatch ({model_state_dict[key].shape} vs {value.shape})")
                else:
                    unmatched_keys.append(f"{key}: not found in model")

            # Load the matched weights
            self.extractor.load_state_dict(matched_weights, strict=False)

            print(f"Successfully loaded VGGT pretrained weights from {checkpoint_path}")
            print(f"Loaded {len(matched_weights)} matching parameters")

            if unmatched_keys:
                print(f"Skipped {len(unmatched_keys)} unmatched parameters:")
                for key in unmatched_keys[:10]:  # Show first 10 unmatched keys
                    print(f"  - {key}")
                if len(unmatched_keys) > 10:
                    print(f"  ... and {len(unmatched_keys) - 10} more")

        except Exception as e:
            print(f"Error loading VGGT pretrained weights: {e}")
            print("Continuing without pretrained weights...")

    def freeze_bn(self):
        """冻结BatchNorm层"""
        for m in self.modules():
            if isinstance(m, nn.BatchNorm2d):
                m.eval()
            if isinstance(m, nn.SyncBatchNorm):
                m.eval()

    def upsample_disp(self, disp, mask_feat_4, stem_2x):
        """视差上采样"""
        xspx = self.spx_2_gru(mask_feat_4, stem_2x)
        spx_pred = self.spx_gru(xspx)
        spx_pred = F.softmax(spx_pred, 1)
        up_disp = context_upsample(disp*4., spx_pred).unsqueeze(1)
        return up_disp

    # def upsample_disp(self, disp, mask_feat_4, rgb):
    #     return self.enhanced_upsampler(disp, mask_feat_4, rgb)

    def hierarchical_disparity_estimation(self, match_left, match_right, features_left):

        # b , 8, 192, h, w
        gwc_volume = build_gwc_volume(match_left, match_right, 768//4, 8)

        # Apply dual self-attention to GWC volume
        b, num_groups, maxdisp, h, w = gwc_volume.shape  # [B, 8, 192, H, W]

        # Step 1: Reshape for disparity dimension attention
        # [B, 8, 192, H, W] -> [B*H*W, 192, 8]
        gwc_reshaped = gwc_volume.permute(0, 3, 4, 2, 1).contiguous()  # [B, H, W, 192, 8]
        gwc_reshaped = gwc_reshaped.view(b * h * w, maxdisp, num_groups)  # [B*H*W, 192, 8]

        # Apply disparity dimension self-attention
        # Reshape for attention: [B*H*W, 192, 8] -> [B*H*W, 8, 192]
        gwc_disp = gwc_reshaped.permute(0, 2, 1)  # [B*H*W, 8, 192]
        gwc_disp_attn = self.disparity_attention(gwc_disp)  # [B*H*W, 8, 192]
        
        
        volume = self.classifier(gwc_disp_attn).squeeze(1)

        prob = F.softmax(volume, dim=1)
        init_disp = disparity_regression(prob, maxdisp=768//4)

        # 为每个样本单独计算视差范围
        batch_size = init_disp.shape[0]
        fine_disp_list = []

        for b in range(batch_size):
            # 计算当前样本的视差范围
            sample_disp = init_disp[b]
            min_disp = int(sample_disp.min().item() - 1)
            max_disp = int(sample_disp.max().item() + 1)

            # 确保范围有效性
            min_disp = max(0, min_disp)  # 确保最小视差不小于0
            max_disp = min(768//4 - 1, max_disp)  # 确保最大视差不超过原始范围
            if max_disp <= min_disp:  # 防止范围无效
                max_disp = min_disp + 8

            # 为当前样本提取对应的代价体切片
            sample_gwc_volume = gwc_volume[b:b+1, :, min_disp:max_disp+1, :, :]
            sample_volume = self.classifier(sample_gwc_volume).squeeze(1)
            sample_prob = F.softmax(sample_volume, dim=1)
            sample_fine_disp = disparity_regression(sample_prob, mindisp=min_disp, maxdisp=max_disp+1)

            fine_disp_list.append(sample_fine_disp)

        # 将所有样本的结果拼接起来
        fine_disp = torch.cat(fine_disp_list, dim=0)

        return fine_disp, prob

    def forward(self, image1, image2, iters=4, flow_init=None, test_mode=False,):
       
        feats = self.extractor(torch.cat([image1[:, None, :, :], image2[:, None, :, :]], dim=1))
        features_left = feats[:, 0, :, :, :]
        features_right = feats[:, 1, :, :, :]

         # 图像预处理
        image1 = (2 * (image1 / 255.0) - 1.0).contiguous()
        image2 = (2 * (image2 / 255.0) - 1.0).contiguous()
        

        # 优化stem特征提取，避免重复计算
        stem_2x = self.stem_2(image1)
        # stem_4_1x = self.stem_4_1(stem_2x)
        # stem_4_2x = self.stem_4_2(stem_2x)
        # stem_4_3x = self.stem_4_3(stem_2x)
        
        # stem_x_list = [stem_4_1x, stem_4_2x, stem_4_3x]

        # 分层自适应立体匹配初始化
        match_left = self.desc(self.conv(features_left))
        match_right = self.desc(self.conv(features_right))
                
        init_disp, prob = self.hierarchical_disparity_estimation(match_left, match_right, features_left)

        # if not test_mode:
        xspx = self.spx_4(features_left)
        xspx = self.spx_2(xspx, stem_2x)  
        spx_pred = self.spx(xspx)
        spx_pred = F.softmax(spx_pred, 1)

        # # 上下文网络初始化
        # net_list = [torch.cat([self.feat_transfer_cnet1(features_left), stem_4_1x], dim=1),
        #              torch.cat([self.feat_transfer_cnet2(features_left), stem_4_2x], dim=1),
        #              torch.cat([self.feat_transfer_cnet3(features_left), stem_4_3x], dim=1),
        # ]

        geo_block = OptimizedCorrelationOnlyGeoEncoding
        geo_fn = geo_block(match_left.float(), match_right.float(), radius=self.args.corr_radius, num_levels=self.args.corr_levels)

        b, c, h, w = match_left.shape
        coords = torch.arange(w, device=match_left.device).float().reshape(1,1,w,1).repeat(b, h, 1, 1).contiguous()
        
        disp = init_disp
       
        disp_preds = []

        f_neig = chunked_by_neighbor_similarity(features_left, kernel_size=7)
        f_context = self.feat_transfer_context(features_left)
        net_list = None
      

        for itr in range(iters):
            disp = disp.detach()
        
            geo_feat = geo_fn(disp, coords, level=2)    
            
            delta_disp, mask_feat_4, net_list = self.update(geo_feat, f_context, f_neig, disp, net_list)
            
            disp = disp + delta_disp
            if test_mode and itr < iters-1:
                continue
            disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x) 
            disp_preds.append(disp_up)

        if test_mode:
            disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x)
            init_disp_up = context_upsample(init_disp*4., spx_pred.float()).unsqueeze(1)
            return disp_up, init_disp_up
        
        init_disp_up = context_upsample(init_disp*4., spx_pred.float()).unsqueeze(1)
        return init_disp_up, disp_preds, prob