import os
os.environ["CUDA_VISIBLE_DEVICES"] = "3"
import sys
sys.path.append("core")

import argparse
import glob
import numpy as np
import torch
from tqdm import tqdm
from pathlib import Path
# from core.monste_plus import Monster
from core.vggt_stereo import VGGT_Ste as Monster
from core.utils.utils import InputPadder
from PIL import Image
from matplotlib import pyplot as plt
import os
import torch.nn.functional as F
import cv2
DEVICE = "cuda"



def load_image(imfile):
    img = np.array(Image.open(imfile)).astype(np.uint8)[..., :3]
    img = torch.from_numpy(img).permute(2, 0, 1).float()
    return img[None].to(DEVICE)


def check_monotonicity_mask(disp):
    H, W = disp.shape
    valid = (disp > 0) & (disp < 1000)
    mono_mask = np.ones((H, W), dtype=bool)
    for y in range(H):
        valid_idx = np.where(valid[y])[0]
        xr = valid_idx - disp[y, valid_idx]
        # Step 1: 检查相邻像素，记录所有违反点对
        for i in range(1, len(valid_idx)):
            if xr[i] < xr[i-1]:
                mono_mask[y, valid_idx[i]] = False  # 右像素为初始违例点
    return mono_mask, valid



def get_file_stem(imfile, dataset):
    """根据数据集类型获取文件名"""
    if dataset == "md":
        return imfile.split("/")[-2]
    elif dataset == "booster":
        return imfile.split("/")[-3] + "_" + imfile.split("/")[-1].split('.')[0]
    elif dataset == "kitti":
        return imfile.split('/')[-1].split('.')[0]  # 去掉扩展名
    elif dataset == "sceneflow":
        return imfile.split("/")[-3] + "_" + imfile.split("/")[-1].split('.')[0]
    elif dataset == "drivingstereo":
        return imfile.split("/")[-3] + "_" + imfile.split("/")[-1].split('.')[0]
    else:
        return imfile.split("/")[-1].split('.')[0]  # 默认使用文件名

def get_dataset_config(dataset):
    """根据数据集类型返回配置信息"""
    configs = {
        "sceneflow": {
            "left_imgs": "/storage/pmj/zhi/dataset/sceneFlow/frames_cleanpass/TRAIN/B/0000/left/*.png",
            "right_imgs": "/storage/pmj/zhi/dataset/sceneFlow/frames_cleanpass/TRAIN/B/0000/right/*.png",
            "output_dir": "output/sceneflow",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },

        "md": {
            "left_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*H/*/im0.png",
            "right_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*H/*/im1.png",
            "output_dir": "output/md",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },
        "kitti": {
            "left_imgs": "/storage/pmj/zhi/dataset/KITTI/KITTI 2015/testing/image_2/*_10.png",
            "right_imgs": "/storage/pmj/zhi/dataset/KITTI/KITTI 2015/testing/image_3/*_10.png",
            "output_dir": "output/kitti",
            "checkpoint": "checkpoints/kitti/step_9000_igev-stereo-kt-ddp.pth"
        },
        "drivingstereo": {
            "left_imgs": "/storage/pmj/zhi/dataset/drivingStereo/s*/left-image-full-size/*.png",
            "right_imgs": "/storage/pmj/zhi/dataset/drivingStereo/s*/right-image-full-size/*.png",
            "output_dir": "output/drivingstereo",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },
        "booster": {
            "left_imgs": "/storage/pmj/zhi/dataset/Booster/test/balanced/*/camera_00/im*.png",
            "right_imgs": "/storage/pmj/zhi/dataset/Booster/test/balanced/*/camera_02/im*.png",
            "output_dir": "output/booster",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        }
    }
    return configs.get(dataset, configs["booster"])  # 默认返回booster配置

def demo(args):
    model = torch.nn.DataParallel(Monster(args), device_ids=[0])

    checkpoint = torch.load(args.restore_ckpt, weights_only=False)

    # 智能处理不同格式的检查点
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    else:
        state_dict = checkpoint

    # 准备加载的状态字典
    model_state = model.state_dict()
    matched_params = {}
    
    # 检查哪些参数可以匹配
    for name, param in state_dict.items():
        model_name = name
        # 处理module前缀问题
        if not name.startswith('module.') and 'module.' + name in model_state:
            model_name = 'module.' + name
        elif name.startswith('module.') and name[7:] in model_state:
            model_name = name[7:]
        
        if model_name in model_state and model_state[model_name].shape == param.shape:
            matched_params[model_name] = param
    
    # 执行部分加载
    try:
        model.load_state_dict(matched_params, strict=False)
    except Exception:
        # 备用方法
        ckpt = dict()
        for key in state_dict:
            if key.startswith('module.'):
                ckpt[key] = state_dict[key]
            else:
                ckpt['module.' + key] = state_dict[key]
        model.load_state_dict(ckpt, strict=False)

    model = model.module
    model.to(DEVICE)
    model.eval()

    output_directory = Path(args.output_directory)
    output_directory.mkdir(parents=True, exist_ok=True)

    with torch.no_grad():
        left_images = sorted(glob.glob(args.left_imgs, recursive=True))
        right_images = sorted(glob.glob(args.right_imgs, recursive=True))
        print(f"Found {len(left_images)} images. Saving files to {output_directory}/")

        for (imfile1, imfile2) in tqdm(list(zip(left_images, right_images))):
            image1 = load_image(imfile1)
            image2 = load_image(imfile2)

            if args.dataset == "booster":
                image1 = image1[:,:,::2,::2]
                image2 = image2[:,:,::2,::2]

            padder = InputPadder(image1.shape, divis_by=64)
            image1, image2 = padder.pad(image1, image2)

            disp, init_disp = model(image1, image2, iters=args.valid_iters, test_mode=True)

            # 转换为numpy并unpad
            disp = disp.cpu().numpy()
            init_disp = init_disp.cpu().numpy()

            disp = padder.unpad(disp)
            init_disp = padder.unpad(init_disp)

            print(f"Disp range: [{disp.min().item():.2f}, {disp.max().item():.2f}]")
            print(f"Init disp , range: [{init_disp.min().item():.2f}, {init_disp.max().item():.2f}] (1/4 resolution)")
            # print(f"Occ mask , range: [{occ_mask.min():.2f}, {occ_mask.max():.2f}]")
            # 根据数据集类型自动选择文件名提取方式
            file_stem = get_file_stem(imfile1, args.dataset)
            # 保存输出图像，统一使用jet颜色映射
            if args.save_separate:
                # 1. 最终视差图 - 使用jet colormap
                plt.imsave(
                    os.path.join(output_directory, f"{file_stem}_disp.png"), 
                    disp.squeeze(), 
                    cmap="jet"
                )
                
                # 2. 初始视差图 - 使用jet colormap
                plt.imsave(
                    os.path.join(output_directory, f"{file_stem}_init_disp.png"), 
                    init_disp.squeeze(), 
                    cmap="jet"
                )
                
            # 保存occ_mask为黑白图
            # occ_mask_np = occ_mask.cpu().numpy().squeeze()
            # occ_mask_img = (occ_mask_np * 255).astype(np.uint8)
            # Image.fromarray(occ_mask_img).save(os.path.join(output_directory, f"{file_stem}_occ_mask.png"))

            # 可选：创建组合图像用于快速比较
            if args.save_combined:
                # 读取原始左视图
                left_img = np.array(Image.open(imfile1))
                if args.dataset == "booster":
                    left_img = left_img[::2, ::2]
                
                # 确保尺寸一致 - 以disp的尺寸为准
                target_h, target_w = disp.shape[-2:]
                
                # 使用matplotlib生成彩色图像
                def apply_colormap_to_array(data, cmap_name):
                    # matplotlib内部会自动进行归一化
                    cmap = plt.cm.get_cmap(cmap_name)
                    colored = cmap(data.squeeze())[:, :, :3]  # 取RGB通道
                    return (colored * 255).astype(np.uint8)
                
                # 生成彩色图像（统一使用jet colormap）
                disp_colored = apply_colormap_to_array(disp, "jet")
                init_disp_colored = apply_colormap_to_array(init_disp, "jet")
                
                # 调整尺寸
                from PIL import Image as PILImage
                
                # 调整左图尺寸
                if left_img.shape[:2] != (target_h, target_w):
                    left_img = np.array(PILImage.fromarray(left_img).resize((target_w, target_h), PILImage.LANCZOS))
                
                # 调整各个图像尺寸到目标尺寸
                def resize_if_needed(img, target_shape, method=PILImage.LANCZOS):
                    if img.shape[:2] != target_shape:
                        return np.array(PILImage.fromarray(img).resize((target_shape[1], target_shape[0]), method))
                    return img
                
                disp_colored = resize_if_needed(disp_colored, (target_h, target_w))
                init_disp_colored = resize_if_needed(init_disp_colored, (target_h, target_w), PILImage.NEAREST)
                
                # 创建1x3布局：[原图, 最终视差, 初始视差]
                combined = np.concatenate([left_img, disp_colored, init_disp_colored], axis=1)
                
                # 可选下采样以减少文件大小
                if args.downsample_combined:
                    combined = combined[::2, ::2]
                
                # 保存组合图像
                combined_filename = os.path.join(output_directory, f"{file_stem}_combined.png")
                PILImage.fromarray(combined).save(combined_filename)
            
            print(f"{file_stem}: 已保存视差图和初始视差图")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    
    # 数据集选择 
    parser.add_argument("--dataset", type=str, choices=["sceneflow", "md", "kitti", "drivingstereo", "booster"], 
                        default="md", help="选择数据集类型")
    
    parser.add_argument("--restore_ckpt", help="restore checkpoint", default="checkpoints/pretrain1/1500_plus.pth")
    parser.add_argument("-l", "--left_imgs", default=None, help="path to all first (left) frames")
    parser.add_argument("-r", "--right_imgs", default=None, help="path to all second (right) frames")
    parser.add_argument("--output_directory", default=None, help="directory to save output")
    
    parser.add_argument("--save_numpy", action="store_true", help="save output as numpy arrays")
    parser.add_argument("--save_separate", action ="store_true", default=True, help="save separate disparity and init_disp images")
    parser.add_argument("--save_combined", action="store_true", help="save combined 1x3 grid image")
    parser.add_argument("--downsample_combined", action="store_true", help="downsample combined image by 2x")
    parser.add_argument("--upscale_init_disp", action="store_true", help="save upscaled init_disp separately (4x resolution)")
    parser.add_argument("--mixed_precision", default=True, help="use mixed precision") 
    parser.add_argument("--precision_dtype",default="bfloat16",choices=["float16", "bfloat16", "float32"],help="Choose precision type: float16 or bfloat16 or float32")
    parser.add_argument("--valid_iters",type=int,default=32,help="number of flow-field updates during forward pass")

    # Architecture choices
    parser.add_argument('--encoder', type=str, default='vitl', choices=['vits', 'vitb', 'vitl', 'vitg'])
    parser.add_argument('--hidden_dims', nargs='+', type=int, default=[128]*3, help="hidden state and context dimensions")
    parser.add_argument('--corr_implementation', choices=["reg", "alt", "reg_cuda", "alt_cuda"], default="reg", help="correlation volume implementation")
    parser.add_argument('--shared_backbone', action='store_true', help="use a single backbone for the context and feature encoders")
    parser.add_argument('--corr_levels', type=int, default=2, help="number of levels in the correlation pyramid")
    parser.add_argument('--corr_radius', type=int, default=4, help="width of the correlation pyramid")
    parser.add_argument('--n_downsample', type=int, default=2, help="resolution of the disparity field (1/2^K)")
    parser.add_argument('--slow_fast_gru', action='store_true', help="iterate the low-res GRUs more frequently")
    parser.add_argument('--n_gru_layers', type=int, default=3, help="number of hidden GRU levels")
    parser.add_argument('--max_disp', type=int, default=768, help="max disp of geometry encoding volume")

    args = parser.parse_args()
    
    # 根据选择的数据集自动配置参数
    dataset_config = get_dataset_config(args.dataset)
    
    # 如果用户没有手动指定这些参数，则使用数据集默认配置
    if args.restore_ckpt is None:
        args.restore_ckpt = dataset_config["checkpoint"]
    if args.left_imgs is None:
        args.left_imgs = dataset_config["left_imgs"]
    if args.right_imgs is None:
        args.right_imgs = dataset_config["right_imgs"]
    if args.output_directory is None:
        args.output_directory = dataset_config["output_dir"]
    
    print(f"使用数据集: {args.dataset}")
    print(f"模型检查点: {args.restore_ckpt}")
    print(f"左图像路径: {args.left_imgs}")
    print(f"右图像路径: {args.right_imgs}")
    print(f"输出目录: {args.output_directory}")
    
    print(f"\n📊 输出图像说明 (统一使用jet colormap: 蓝→青→绿→黄→红):")
    print(f"   1. disp.png - 最终视差图")
    print(f"   2. init_disp.png - 初始视差图")
    if args.save_combined:
        print(f"   3. combined.png - 1x3组合图像")
    print("-" * 60)
    
    demo(args)
