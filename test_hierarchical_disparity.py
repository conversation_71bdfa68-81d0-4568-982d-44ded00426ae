#!/usr/bin/env python3
"""
Test script for hierarchical disparity estimation with GWC Volume Attention
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
sys.path.append('./vggt')

from core.submodule import build_gwc_volume, disparity_regression
from core.dssm import apply_windowed_attention
from vggt.layers.block import Block


class TestHierarchicalDisparity(nn.Module):
    """Test module for hierarchical disparity estimation with attention"""
    
    def __init__(self):
        super().__init__()
        
        # Classifier for disparity estimation
        self.classifier = nn.Sequential(
            nn.Conv3d(8, 16, 3, 1, 1, bias=False),
            nn.GELU(),
            nn.InstanceNorm3d(16),
            nn.Conv3d(16, 16, 3, 1, 1, bias=False),
            nn.GELU(),
            nn.InstanceNorm3d(16),
            nn.Conv3d(16, 1, 3, 1, 1, bias=False)
        )
        
        # GWC Volume Attention modules
        # Disparity dimension self-attention (for 192 channels)
        self.disparity_attention = Block(
            dim=192,  # 192 channels
            num_heads=8,
            mlp_ratio=4.0,
            qkv_bias=True,
            drop=0.0,
            attn_drop=0.0,
            drop_path=0.0
        )
        
        # Spatial dimension self-attention (for 8 disparity levels)
        self.spatial_attention = Block(
            dim=8,  # 8 disparity levels
            num_heads=4,
            mlp_ratio=4.0,
            qkv_bias=True,
            drop=0.0,
            attn_drop=0.0,
            drop_path=0.0
        )
    
    def hierarchical_disparity_estimation(self, match_left, match_right, features_left=None):
        """
        Hierarchical disparity estimation with dual self-attention on GWC volume
        """
        
        # Build GWC volume: [B, 8, 192, H, W]
        gwc_volume = build_gwc_volume(match_left, match_right, 768//4, 8)  
        
        # Apply dual self-attention to GWC volume
        b, num_groups, maxdisp, h, w = gwc_volume.shape  # [B, 8, 192, H, W]
        
        print(f"Original GWC volume shape: {gwc_volume.shape}")
        
        # Step 1: Reshape for disparity dimension attention
        # [B, 8, 192, H, W] -> [B*H*W, 192, 8]
        gwc_reshaped = gwc_volume.permute(0, 3, 4, 2, 1).contiguous()  # [B, H, W, 192, 8]
        gwc_reshaped = gwc_reshaped.view(b * h * w, maxdisp, num_groups)  # [B*H*W, 192, 8]
        
        # Apply disparity dimension self-attention
        # Reshape for attention: [B*H*W, 192, 8] -> [B*H*W, 8, 192]
        gwc_disp = gwc_reshaped.permute(0, 2, 1)  # [B*H*W, 8, 192]
        gwc_disp_attn = self.disparity_attention(gwc_disp)  # [B*H*W, 8, 192]
        # Reshape back: [B*H*W, 8, 192] -> [B*H*W, 192, 8]
        gwc_disp_attn = gwc_disp_attn.permute(0, 2, 1)  # [B*H*W, 192, 8]
        
        print(f"After disparity attention: {gwc_disp_attn.shape}")
        
        # Step 2: Reshape for spatial windowed attention
        # [B*H*W, 192, 8] -> [B*192, H, W, 8] -> [B*192, 8, H, W]
        gwc_spatial = gwc_disp_attn.view(b, h, w, maxdisp, num_groups)  # [B, H, W, 192, 8]
        gwc_spatial = gwc_spatial.permute(0, 3, 4, 1, 2)  # [B, 192, 8, H, W]
        gwc_spatial = gwc_spatial.contiguous().view(b * maxdisp, num_groups, h, w)  # [B*192, 8, H, W]
        
        # Apply spatial windowed self-attention with window size 7
        gwc_spatial_attn = apply_windowed_attention(gwc_spatial, self.spatial_attention, window_size=7)
        
        print(f"After spatial attention: {gwc_spatial_attn.shape}")
        
        # Reshape back to original format: [B*192, 8, H, W] -> [B, 8, 192, H, W]
        gwc_spatial_attn = gwc_spatial_attn.view(b, maxdisp, num_groups, h, w)  # [B, 192, 8, H, W]
        gwc_volume_attn = gwc_spatial_attn.permute(0, 2, 1, 3, 4)  # [B, 8, 192, H, W]

        print(f"Final GWC volume shape: {gwc_volume_attn.shape}")
        
        # Apply classifier to get disparity probabilities
        volume = self.classifier(gwc_volume_attn).squeeze(1)
        prob = F.softmax(volume, dim=1)
        init_disp = disparity_regression(prob, maxdisp=768//4)
        
        print(f"Disparity volume shape: {volume.shape}")
        print(f"Probability shape: {prob.shape}")
        print(f"Initial disparity shape: {init_disp.shape}")
        
        # For demonstration, we'll skip the fine disparity estimation loop
        # and just return the initial disparity
        fine_disp = init_disp
        
        return fine_disp, prob


def test_hierarchical_disparity():
    """Test the hierarchical disparity estimation with attention"""
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create test model
    model = TestHierarchicalDisparity().to(device)
    
    # Create test data
    batch_size = 1
    height, width = 112, 112  # 1/4 scale of 448x448
    channels = 96  # Match feature channels
    
    # Create dummy match features
    match_left = torch.randn(batch_size, channels, height, width).to(device)
    match_right = torch.randn(batch_size, channels, height, width).to(device)
    
    print(f"\nInput shapes:")
    print(f"  match_left: {match_left.shape}")
    print(f"  match_right: {match_right.shape}")
    
    # Run hierarchical disparity estimation
    print(f"\nRunning hierarchical disparity estimation...")
    
    with torch.no_grad():
        fine_disp, prob = model.hierarchical_disparity_estimation(match_left, match_right)
    
    print(f"\nOutput shapes:")
    print(f"  fine_disp: {fine_disp.shape}")
    print(f"  prob: {prob.shape}")
    
    # Check disparity range
    print(f"\nDisparity statistics:")
    print(f"  Min disparity: {fine_disp.min().item():.2f}")
    print(f"  Max disparity: {fine_disp.max().item():.2f}")
    print(f"  Mean disparity: {fine_disp.mean().item():.2f}")
    
    # Calculate parameter counts
    total_params = sum(p.numel() for p in model.parameters())
    attention_params = sum(p.numel() for p in model.disparity_attention.parameters()) + \
                     sum(p.numel() for p in model.spatial_attention.parameters())
    classifier_params = sum(p.numel() for p in model.classifier.parameters())
    
    print(f"\nParameter counts:")
    print(f"  Total parameters: {total_params:,}")
    print(f"  Attention parameters: {attention_params:,} ({attention_params/total_params*100:.1f}%)")
    print(f"  Classifier parameters: {classifier_params:,} ({classifier_params/total_params*100:.1f}%)")
    
    print(f"\nHierarchical disparity estimation test completed successfully!")
    
    return fine_disp, prob


if __name__ == "__main__":
    test_hierarchical_disparity()
